import os
import pandas as pd
import pandas_ta as ta
import logger_config
from datetime import datetime, timedelta
from load_parquet_in_memory import load_parquet_data, list_date_folders
import sys, numpy
from dotenv import load_dotenv
from typing import Optional
import time
from BT_Expiry_New_Recreate_Strategy import execute_Sniper_filtered

logger = logger_config.setup_logger('main_logger', 'main.log')
logger = logger_config.get_logger("main_logger")

load_dotenv()  # Load environment variables from .env file if present

MACD_FAST = int(os.getenv("MACD_FAST", 12))
MACD_SLOW = int(os.getenv("MACD_SLOW", 26))
MACD_SIGNAL = int(os.getenv("MACD_SIGNAL", 9))
ST_LENGTH = int(os.getenv("ST_LENGTH", 10))
ST_MULTIPLIER = float(os.getenv("ST_MULTIPLIER", 3.0))
WARMUP_DAYS = int(os.getenv("WARMUP_DAYS", 5))  # Number of previous calendar days to include for indicator warmup

# === Indicators ===
def add_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """
    Input df columns: ['datetime','Open','High','Low','Close','Volume'].
    Adds MACD, VWAP, and Supertrend columns (from pandas-ta).
    Ensures a DatetimeIndex during indicator computation (required by pandas-ta's VWAP),
    then resets back to a column.
    """
    df = df.copy()
    if len(df) < 30:
        logger.warning("Not enough rows to compute indicators (need >= 30). Returning as-is.")
        return df

    if "datetime" not in df.columns:
        raise ValueError("add_indicators expects a 'datetime' column")

    # Ensure datetime is proper dtype and sorted
    df["datetime"] = pd.to_datetime(df["datetime"], errors="coerce")
    df = df.dropna(subset=["datetime"]).sort_values("datetime")

    # Temporarily set DatetimeIndex for pandas-ta (VWAP requires it)
    idx_backup: Optional[pd.Index] = df.index
    df = df.set_index("datetime")
    #print(df.head())

    # Compute indicators
    macd_df = ta.macd(df["Close"], fast=MACD_FAST, slow=MACD_SLOW, signal=MACD_SIGNAL)
    #print("MACD:",macd_df.head(30))
    supertrend_df = ta.supertrend(df["High"], df["Low"], df["Close"], length=ST_LENGTH, multiplier=ST_MULTIPLIER)
    #print("Supertrend:",supertrend_df.head(30))

    
    # Join to original
    df = df.join(macd_df)
    df = df.join(supertrend_df)

   
    # Reset index back to a datetime column
    df = df.reset_index().rename(columns={"index": "datetime"})

    # Ensure the column order roughly matches expectations
    # Keep extra indicator columns appended at the end
    base_cols = ["datetime", "Open", "High", "Low", "Close", "Volume"]
    ordered_cols = [c for c in base_cols if c in df.columns] + [c for c in df.columns if c not in base_cols]
    df = df[ordered_cols]

    return df


# === Generate Signal (MACD + VWAP + Supertrend + PCR) ===
def generate_signal(df: pd.DataFrame, pcr_df: Optional[pd.DataFrame] = None) -> pd.DataFrame:
    """
    df: candles + indicators; optional pcr_df: ['datetime','pcr'].
    Returns df with a 'signal' column in ['buy','sell', None].
    """
    merged = df.sort_values("datetime").copy()

    signals = []
    for _, row in merged.iterrows():
        macd = row.get("MACD_12_26_9")
        macds = row.get("MACDs_12_26_9")
        supertd = row.get("SUPERTd_10_3.0")
        #vwap = row.get("VWAP")
        close = row.get("Close")

        # Basic checks for required indicators
        if pd.isna(macd) or pd.isna(macds) or pd.isna(supertd) or pd.isna(close):
            signals.append(None)
            continue

        macd_signal = "buy" if macd > macds else "sell"
        #vwap_signal = "buy" if close > vwap else "sell"
        supertrend_signal = "buy" if supertd == 1 else "sell"

        final_signal = macd_signal if (macd_signal == supertrend_signal) else None

        signals.append(final_signal)

    merged["signal"] = signals
    return merged


def _select_one_signal_per_day(signals_df: pd.DataFrame,
                               start_time: datetime.time,
                               cutoff_time: datetime.time) -> pd.DataFrame:
    """
    Select the earliest valid signal per day within [start_time, cutoff_time].

    signals_df: DataFrame with columns ['datetime', 'signal', ...],
                where 'datetime' is a pandas.Timestamp and 'signal' is 'buy'/'sell'/None.

    Returns:
        DataFrame containing at most one row per day, the first valid signal within the window.
    """
    if signals_df.empty:
        return signals_df

    # Filter to the time window and valid signals
    mask = (
        (signals_df["datetime"].dt.time >= start_time) &
        (signals_df["datetime"].dt.time <= cutoff_time) &
        (signals_df["signal"].notna())
    )
    window_df = signals_df.loc[mask].copy()

    if window_df.empty:
        logger.info("No valid signals within the selection window.")
        return window_df

    # Sort by datetime and pick first per day
    window_df = window_df.sort_values("datetime")
    window_df["trade_date"] = window_df["datetime"].dt.date
    first_per_day = window_df.drop_duplicates(subset=["trade_date"], keep="first").drop(columns=["trade_date"])

    logger.info(f"Selected {len(first_per_day)} signals (one per day) between {start_time} and {cutoff_time}.")
    return first_per_day


def process_signals_and_execute_trades(signals_df, from_date, to_date):
    """
    Process signals and execute option trades based on MACD + Supertrend signals.

    Enforces at most one signal per day, considering only signals up to 14:45.
    """
    # Trading window for signal generation
    start_time = datetime.strptime("09:15", "%H:%M").time()
    cutoff_time = datetime.strptime("14:45", "%H:%M").time()  # Only generate signals till 2:45 pm
    exit_time = datetime.strptime("15:15", "%H:%M").time()

    # Compute and select at most one signal per day within the time window
    one_per_day_signals = _select_one_signal_per_day(signals_df, start_time, cutoff_time)
    #one_per_day_signals.to_csv("one_per_day_signals.csv", index=False)
    print(one_per_day_signals.head())
    
    if one_per_day_signals.empty:
        logger.info("No valid trading signals found in the specified time window (one per day rule).")
        return

    logger.info(f"Processing {len(one_per_day_signals)} signals (max one per day).")

    # Load parquet data for the date range
    root_path = r"C:\Users\<USER>\YatinBhatia\Local_Deployment\Sniper_BT_V1 - Copy\Parquet_Files\Thursday_output_folder"
    all_folders = list_date_folders(root_path, from_date, to_date)

    if not all_folders:
        logger.warning("No option data folders found for the specified date range.")
        return

    data = load_parquet_data(all_folders)

    # Process each selected signal
    for _, signal_row in one_per_day_signals.iterrows():
        current_signal = signal_row["signal"]
        current_datetime = signal_row["datetime"]
        signal_date = current_datetime.date()
        signal_time = current_datetime.time()

        logger.info("\n=== Processing Signal (one-per-day) ===")
        logger.info(f"DateTime: {current_datetime}")
        logger.info(f"Signal: {current_signal}")

        # Find matching date folder in parquet data
        matching_folder = None
        for mainkey in data.keys():
            folder_date_str = os.path.basename(mainkey)
            try:
                folder_date = datetime.strptime(folder_date_str, "%Y%m%d").date()
                if folder_date == signal_date:
                    matching_folder = mainkey
                    break
            except ValueError:
                continue

        if not matching_folder:
            logger.warning(f"No option data found for signal date: {signal_date}")
            continue

        # Determine which option types to process based on signal
        if current_signal == "buy":
            target_option_types = ["CE_SELL"]
            logger.info("BUY SIGNAL -> Processing CE options")
        elif current_signal == "sell":
            target_option_types = ["PE_SELL"]
            logger.info("SELL SIGNAL -> Processing PE options")
        else:
            logger.warning(f"Unknown signal type: {current_signal}")
            continue

        # Execute option strategy for this signal
        try:
            folder_date_dt = datetime.strptime(os.path.basename(matching_folder), "%Y%m%d")
            results = execute_Sniper_filtered(
                matching_folder,
                data[matching_folder],
                signal_time,
                exit_time,
                folder_date_dt,
                target_option_types
            )

            if results:
                logger.info(f"Successfully executed trades for {current_signal} signal")

                # Log price validation results for each option
                for option_type, option_data in results[0].items():
                    if isinstance(option_data, dict) and 'price_validation' in option_data:
                        validation = option_data['price_validation']
                        logger.info(f"{option_type} - {option_data['symbol']}: {validation.get('recommendation', 'unknown')} - {validation.get('reason', 'no reason')}")

                logger.info(f"Results: {results}")
            else:
                logger.warning(f"No trades executed for {current_signal} signal")

        except Exception as e:
            logger.exception(f"Error executing trades for signal at {current_datetime}: {e}")

# === Load NIFTY 50 index candles from CSV ===
def load_nifty_index_csv(csv_path: str) -> pd.DataFrame:
    """
    Load an index CSV with columns like: date,open,high,low,close,volume
    Returns standardized DataFrame: ['datetime','Open','High','Low','Close','Volume'] sorted by datetime.
    """
    if not os.path.exists(csv_path):
        raise FileNotFoundError(f"Index CSV not found: {csv_path}")

    df = pd.read_csv(csv_path)
    # Normalize column names to lower
    df.columns = [c.strip().lower() for c in df.columns]

    rename_map = {
        "date": "datetime",
        "datetime": "datetime",
        "open": "Open",
        "high": "High",
        "low": "Low",
        "close": "Close",
        "volume": "Volume",
        "vol": "Volume",
    }
    df = df.rename(columns=rename_map)

    required = ["datetime", "Open", "High", "Low", "Close", "Volume"]
    missing = [c for c in required if c not in df.columns]
    if missing:
        raise ValueError(f"Index CSV is missing required columns: {missing}")

    df["datetime"] = pd.to_datetime(df["datetime"])
    df = df[required].sort_values("datetime").reset_index(drop=True)
    return df


# === Main Runner ===
def main():
    root_path = r"C:\Users\<USER>\YatinBhatia\Local_Deployment\Sniper_BT_V1 - Copy\Parquet_Files\Thursday_output_folder"

    # Retrieve start and end dates from environment variables
    start_date_str = os.getenv("START_DATE")
    end_date_str = os.getenv("END_DATE")
    index_csv_path = os.getenv("INDEX_CSV_PATH")  # Path to NIFTY 50 one-minute CSV
    print(f"Index CSV Path: {index_csv_path}")

    if not start_date_str or not end_date_str:
        logger.error("START_DATE and END_DATE environment variables must be set (format: YYYYMMDD).")
        return

    try:
        from_date = datetime.strptime(start_date_str, "%Y%m%d")
        to_date = datetime.strptime(end_date_str, "%Y%m%d")
    except ValueError as e:
        logger.error(f"Invalid date format in environment variables: {e}")
        return

    
    # 2) Load NIFTY 50 index candles for indicators
    if not index_csv_path:
        logger.error("INDEX_CSV_PATH environment variable is not set.")
        return

    try:
        candles = load_nifty_index_csv(index_csv_path)
    except Exception as e:
        logger.error(f"Failed to load index CSV: {e}")
        return
    
    # Warmup window: include previous WARMUP_DAYS of candles to prime ATR/Supertrend (and MACD)
    warmup_start = from_date - timedelta(days=WARMUP_DAYS)
    print(f"Using warmup window from {warmup_start} to {to_date} (WARMUP_DAYS={WARMUP_DAYS})")

    candles_window = candles[(candles["datetime"] >= warmup_start) & (candles["datetime"] <= to_date)].copy()
    if candles_window.empty:
        logger.error("No index candles in the warmup+backtest window. Check CSV date coverage and env dates.")
        return

    #print(candles_window.head())

    # 3) Add indicators using the warmup window
    candles_with_ind = add_indicators(candles_window)

    # 4) Trim back to the original backtest window to avoid initial NaNs from warmup
    candles_trimmed = candles_with_ind[
        (candles_with_ind["datetime"] >= from_date) & (candles_with_ind["datetime"] <= to_date)
    ].copy()

    if candles_trimmed.empty:
        logger.error("No index candles in the requested backtest date range after trimming.")
        return
    print(candles_trimmed.head())

    # 5) Generate signals using indicators
    signals_df = generate_signal(candles_trimmed)
    # 6) Process signals and execute option trades
    process_signals_and_execute_trades(signals_df, from_date, to_date)

    
if __name__ == "__main__":
    main()